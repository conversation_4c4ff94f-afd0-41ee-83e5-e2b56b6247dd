#!/usr/bin/env python3
"""
详细的MCP服务诊断脚本
"""
import os
import sys
import json
import subprocess
import time
import logging

# 添加项目根目录到Python路径
sys.path.append('/app')

from functions.mcp_server import MCPServerManager

def main():
    """主函数"""
    print("=== 详细MCP服务诊断 ===")
    
    # 1. 检查配置文件
    print("\n1. 检查配置文件:")
    config_path = "config/mcp_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"   ✅ 配置文件存在: {config_path}")
        print(f"   📋 服务数量: {len(config.get('mcpServers', {}))}")
        for name, server_config in config.get('mcpServers', {}).items():
            enabled = server_config.get('enabled', False)
            connection_type = server_config.get('connection_type', 'stdio')
            print(f"   - {name}: {'✅ 启用' if enabled else '❌ 禁用'} ({connection_type})")
    else:
        print(f"   ❌ 配置文件不存在: {config_path}")
        return
    
    # 2. 检查PID文件目录权限
    print("\n2. 检查PID文件目录权限:")
    pid_dir = "config/mcp_pids"
    try:
        # 检查目录是否存在
        if not os.path.exists(pid_dir):
            os.makedirs(pid_dir, exist_ok=True)
            print(f"   ✅ 创建PID目录: {pid_dir}")
        
        # 测试写入权限
        test_file = os.path.join(pid_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"   ✅ PID目录写入权限正常")
        
        # 列出现有PID文件
        pid_files = os.listdir(pid_dir)
        print(f"   📁 现有PID文件: {pid_files}")
        
        for pid_file in pid_files:
            if pid_file.endswith('.pid'):
                pid_path = os.path.join(pid_dir, pid_file)
                try:
                    with open(pid_path, 'r') as f:
                        pid = f.read().strip()
                    print(f"   📄 {pid_file}: PID={pid}")
                    
                    # 检查进程是否存在
                    try:
                        os.kill(int(pid), 0)
                        print(f"     ✅ 进程存在")
                    except (OSError, ProcessLookupError, ValueError):
                        print(f"     ❌ 进程不存在")
                except Exception as e:
                    print(f"     ❌ 读取失败: {e}")
                    
    except Exception as e:
        print(f"   ❌ PID目录操作失败: {e}")
    
    # 3. 检查当前运行的进程
    print("\n3. 检查当前运行的进程:")
    try:
        result = subprocess.run(
            ["ps", "aux"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        lines = result.stdout.split('\n')
        npx_processes = [line for line in lines if 'npx' in line and 'grep' not in line]
        filesystem_processes = [line for line in lines if 'filesystem' in line and 'grep' not in line]
        
        print(f"   🔍 NPX进程数: {len(npx_processes)}")
        for process in npx_processes:
            print(f"     {process.strip()}")
            
        print(f"   🔍 Filesystem进程数: {len(filesystem_processes)}")
        for process in filesystem_processes:
            print(f"     {process.strip()}")
            
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
    
    # 4. 测试MCP服务管理器
    print("\n4. 测试MCP服务管理器:")
    try:
        manager = MCPServerManager()
        
        # 检查所有服务状态
        all_status = manager.get_all_server_status()
        print("   📊 服务状态:")
        for name, status in all_status.items():
            print(f"     {name}: {status}")
        
        # 检查files服务的详细日志
        if 'files' in all_status:
            print("\n   📋 files服务详细信息:")
            logs = manager.get_server_logs('files')
            print(f"     状态: {logs.get('status', '未知')}")
            print(f"     命令: {logs.get('command', '无')}")
            print(f"     启动时间: {logs.get('start_time', 0)}")
            
            stdout_logs = logs.get('stdout', [])
            if stdout_logs:
                print("     标准输出 (最后10条):")
                for log in stdout_logs[-10:]:
                    print(f"       {log}")
            
            stderr_logs = logs.get('stderr', [])
            if stderr_logs:
                print("     错误输出 (最后10条):")
                for log in stderr_logs[-10:]:
                    print(f"       {log}")
        
    except Exception as e:
        print(f"   ❌ MCP服务管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 手动启动测试
    print("\n5. 手动启动测试:")
    try:
        # 检查npx是否可用
        result = subprocess.run(
            ["which", "npx"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            npx_path = result.stdout.strip()
            print(f"   ✅ npx可用: {npx_path}")
            
            # 尝试启动filesystem服务
            print("   🚀 尝试手动启动filesystem服务...")
            process = subprocess.Popen(
                ["npx", "-y", "@modelcontextprotocol/server-filesystem", "."],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print(f"   📍 进程PID: {process.pid}")
            
            # 等待一下看进程是否立即退出
            time.sleep(3)
            
            if process.poll() is None:
                print("   ✅ 进程正在运行")
                
                # 测试PID文件保存
                test_pid_file = f"config/mcp_pids/manual_test.pid"
                try:
                    with open(test_pid_file, 'w') as f:
                        f.write(str(process.pid))
                    print(f"   ✅ PID文件保存成功: {test_pid_file}")
                    
                    # 测试PID检查
                    try:
                        os.kill(process.pid, 0)
                        print(f"   ✅ PID检查成功: {process.pid}")
                    except (OSError, ProcessLookupError):
                        print(f"   ❌ PID检查失败: {process.pid}")
                    
                    # 清理测试文件
                    os.remove(test_pid_file)
                    
                except Exception as e:
                    print(f"   ❌ PID文件操作失败: {e}")
                
                # 终止测试进程
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                print("   🛑 测试进程已终止")
            else:
                return_code = process.returncode
                stdout, stderr = process.communicate()
                print(f"   ❌ 进程立即退出，返回码: {return_code}")
                if stdout:
                    print(f"   📤 标准输出: {stdout}")
                if stderr:
                    print(f"   📤 错误输出: {stderr}")
        else:
            print("   ❌ npx不可用")
            
    except Exception as e:
        print(f"   ❌ 手动启动测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. 尝试通过管理器重新启动
    print("\n6. 尝试通过管理器重新启动files服务:")
    try:
        manager = MCPServerManager()
        
        # 先停止服务
        print("   🛑 停止files服务...")
        stop_result = manager.stop_server('files')
        print(f"   停止结果: {stop_result}")
        
        time.sleep(1)
        
        # 启动服务
        print("   🚀 启动files服务...")
        start_result = manager.start_server('files')
        print(f"   启动结果: {start_result}")
        
        time.sleep(2)
        
        # 检查状态
        status = manager.get_server_status('files')
        print(f"   当前状态: {status}")
        
        # 检查最新日志
        logs = manager.get_server_logs('files')
        print(f"   日志状态: {logs.get('status', '未知')}")
        
        recent_stdout = logs.get('stdout', [])[-5:]
        if recent_stdout:
            print("   最新标准输出:")
            for log in recent_stdout:
                print(f"     {log}")
        
        recent_stderr = logs.get('stderr', [])[-5:]
        if recent_stderr:
            print("   最新错误输出:")
            for log in recent_stderr:
                print(f"     {log}")
                
    except Exception as e:
        print(f"   ❌ 管理器重启测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
