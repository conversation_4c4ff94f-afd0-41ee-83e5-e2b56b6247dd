#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务状态调试脚本
用于检查MCP服务的状态显示问题
"""

import json
import os
import sys
import time


def debug_mcp_status():
    """调试MCP服务状态"""
    print("=== MCP服务状态调试 ===\n")

    # 1. 检查配置文件
    print("1. 配置文件内容:")
    config_path = "config/mcp_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(json.dumps(config, indent=2, ensure_ascii=False))

        # 分析配置
        servers = config.get("mcpServers", {})
        print(f"\n总服务数: {len(servers)}")

        for server_name, server_config in servers.items():
            enabled = server_config.get("enabled", False)
            connection_type = server_config.get("connection_type", "stdio")
            print(f"\n服务: {server_name}")
            print(f"  - enabled: {enabled}")
            print(f"  - connection_type: {connection_type}")
            if "command" in server_config:
                print(f"  - command: {server_config['command']}")
                print(f"  - args: {server_config.get('args', [])}")
    else:
        print("配置文件不存在")
    print()


def analyze_status_logic():
    """分析状态逻辑"""
    print("=== 状态逻辑分析 ===\n")

    config_path = "config/mcp_config.json"
    if not os.path.exists(config_path):
        print("配置文件不存在")
        return

    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)

    servers = config.get("mcpServers", {})

    for server_name, server_config in servers.items():
        enabled = server_config.get("enabled", False)
        connection_type = server_config.get("connection_type", "stdio")

        print(f"服务: {server_name}")
        print(f"  配置状态: {'启用' if enabled else '禁用'}")
        print(f"  连接类型: {connection_type}")

        # 模拟状态检测逻辑
        if not enabled:
            expected_status = "disabled"
        elif connection_type == "http":
            expected_status = "running"  # HTTP服务只要enabled就是running
        else:
            expected_status = "stopped"  # stdio服务需要检查进程状态

        print(f"  预期状态: {expected_status}")

        # 状态显示映射
        status_display = {
            "running": "🟢 运行中",
            "stopped": "🔴 已停止",
            "disabled": "⚪ 已禁用",
        }.get(expected_status, "❓ 未知")

        print(f"  显示状态: {status_display}")
        print()


if __name__ == "__main__":
    debug_mcp_status()
    analyze_status_logic()
