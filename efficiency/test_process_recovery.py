#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进程恢复功能
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟导入（避免MCP依赖问题）
try:
    from functions.mcp_server import mcp_server_manager
    print("✅ 成功导入MCP服务器管理器")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("创建模拟的MCP服务器管理器...")
    
    # 创建一个简化的管理器用于测试
    import json
    import subprocess
    
    class MockMCPServerManager:
        def __init__(self):
            self.config_path = "config/mcp_config.json"
            self.pid_file_dir = "config/mcp_pids"
            self.server_processes = {}
            self.server_logs = {}
            self.config = self._load_config()
            
            # 创建PID文件目录
            os.makedirs(self.pid_file_dir, exist_ok=True)
            print(f"✅ 创建PID文件目录: {self.pid_file_dir}")
        
        def _load_config(self):
            try:
                if os.path.exists(self.config_path):
                    with open(self.config_path, "r", encoding="utf-8") as f:
                        return json.load(f)
                else:
                    return {"mcpServers": {}}
            except Exception as e:
                print(f"加载配置失败: {e}")
                return {"mcpServers": {}}
        
        def get_all_servers(self):
            return self.config.get("mcpServers", {})
        
        def _get_pid_file_path(self, server_name):
            return os.path.join(self.pid_file_dir, f"{server_name}.pid")
        
        def _save_pid(self, server_name, pid):
            try:
                pid_file = self._get_pid_file_path(server_name)
                with open(pid_file, 'w') as f:
                    f.write(str(pid))
                print(f"✅ 保存PID文件: {pid_file} -> {pid}")
            except Exception as e:
                print(f"❌ 保存PID文件失败: {server_name}, {e}")
        
        def _load_pid(self, server_name):
            try:
                pid_file = self._get_pid_file_path(server_name)
                if os.path.exists(pid_file):
                    with open(pid_file, 'r') as f:
                        pid = int(f.read().strip())
                    print(f"✅ 加载PID文件: {pid_file} -> {pid}")
                    return pid
            except Exception as e:
                print(f"❌ 加载PID文件失败: {server_name}, {e}")
            return None
        
        def _is_process_running(self, pid):
            try:
                os.kill(pid, 0)
                return True
            except (OSError, ProcessLookupError):
                return False
        
        def start_server(self, server_name):
            server_config = self.get_all_servers().get(server_name)
            if not server_config or not server_config.get("enabled", False):
                print(f"⚪ 服务未启用: {server_name}")
                return False
            
            connection_type = server_config.get("connection_type", "stdio")
            if connection_type == "http":
                print(f"🌐 HTTP服务无需启动本地进程: {server_name}")
                return True
            
            if "command" not in server_config:
                print(f"❌ 服务器配置缺少command字段: {server_name}")
                return False
            
            try:
                command = [server_config["command"]] + server_config.get("args", [])
                print(f"🚀 启动服务: {server_name}")
                print(f"   命令: {' '.join(command)}")
                
                process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1
                )
                
                self.server_processes[server_name] = process
                self._save_pid(server_name, process.pid)
                
                time.sleep(1)
                if process.poll() is None:
                    print(f"✅ 服务启动成功: {server_name} (PID: {process.pid})")
                    return True
                else:
                    print(f"❌ 服务启动失败: {server_name}")
                    return False
                    
            except Exception as e:
                print(f"❌ 启动服务失败: {server_name}, 错误: {e}")
                return False
        
        def get_server_status(self, server_name):
            server_config = self.get_all_servers().get(server_name)
            if not server_config:
                return "not_found"
            
            if not server_config.get("enabled", False):
                return "disabled"
            
            connection_type = server_config.get("connection_type", "stdio")
            if connection_type == "http":
                return "running"
            
            if server_name in self.server_processes:
                process = self.server_processes[server_name]
                if process.poll() is None:
                    return "running"
                else:
                    del self.server_processes[server_name]
                    return "stopped"
            
            return "stopped"
        
        def show_status(self):
            print("\n=== MCP服务状态 ===")
            servers = self.get_all_servers()
            
            for server_name, server_config in servers.items():
                status = self.get_server_status(server_name)
                enabled = server_config.get("enabled", False)
                connection_type = server_config.get("connection_type", "stdio")
                
                status_icon = {
                    "running": "🟢",
                    "stopped": "🔴",
                    "disabled": "⚪",
                    "not_found": "❓"
                }.get(status, "❓")
                
                status_text = {
                    "running": "运行中",
                    "stopped": "已停止",
                    "disabled": "已禁用",
                    "not_found": "未找到"
                }.get(status, "未知")
                
                print(f"{status_icon} {server_name} - {status_text} (enabled: {enabled}, type: {connection_type})")
        
        def check_recovery(self):
            print("\n=== 检查进程恢复 ===")
            servers = self.get_all_servers()
            
            for server_name in servers:
                pid = self._load_pid(server_name)
                if pid:
                    if self._is_process_running(pid):
                        print(f"✅ 发现运行中的进程: {server_name} (PID: {pid})")
                    else:
                        print(f"❌ 进程已结束: {server_name} (PID: {pid})")
                else:
                    print(f"⚪ 无PID文件: {server_name}")
    
    mcp_server_manager = MockMCPServerManager()


def test_process_recovery():
    """测试进程恢复功能"""
    print("=== 进程恢复测试 ===\n")
    
    # 1. 显示当前状态
    print("1. 当前状态:")
    mcp_server_manager.show_status()
    
    # 2. 检查PID文件目录
    print(f"\n2. PID文件目录: {mcp_server_manager.pid_file_dir}")
    if os.path.exists(mcp_server_manager.pid_file_dir):
        pid_files = os.listdir(mcp_server_manager.pid_file_dir)
        print(f"   PID文件: {pid_files}")
    else:
        print("   PID文件目录不存在")
    
    # 3. 检查进程恢复
    mcp_server_manager.check_recovery()
    
    # 4. 启动一个服务进行测试
    print("\n3. 启动files服务进行测试:")
    success = mcp_server_manager.start_server("files")
    
    if success:
        print("\n4. 启动后状态:")
        mcp_server_manager.show_status()
        
        print("\n5. 检查PID文件:")
        if os.path.exists(mcp_server_manager.pid_file_dir):
            pid_files = os.listdir(mcp_server_manager.pid_file_dir)
            print(f"   PID文件: {pid_files}")
            
            for pid_file in pid_files:
                if pid_file.endswith('.pid'):
                    pid_path = os.path.join(mcp_server_manager.pid_file_dir, pid_file)
                    with open(pid_path, 'r') as f:
                        pid = f.read().strip()
                    print(f"   {pid_file}: {pid}")


if __name__ == "__main__":
    test_process_recovery()
