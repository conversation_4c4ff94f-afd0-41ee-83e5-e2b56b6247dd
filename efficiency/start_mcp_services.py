#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动启动MCP服务的脚本
"""

import json
import os
import subprocess
import sys
import time


class SimpleMCPManager:
    """简化的MCP管理器，用于测试"""
    
    def __init__(self, config_path="config/mcp_config.json"):
        self.config_path = config_path
        self.server_processes = {}
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            else:
                return {"mcpServers": {}}
        except Exception as e:
            print(f"加载配置失败: {e}")
            return {"mcpServers": {}}
    
    def get_all_servers(self):
        """获取所有服务器配置"""
        return self.config.get("mcpServers", {})
    
    def start_server(self, server_name):
        """启动服务器"""
        server_config = self.get_all_servers().get(server_name)
        if not server_config:
            print(f"❌ 服务器配置不存在: {server_name}")
            return False
        
        if not server_config.get("enabled", False):
            print(f"⚪ 服务器未启用: {server_name}")
            return False
        
        connection_type = server_config.get("connection_type", "stdio")
        
        # HTTP类型服务不需要启动本地进程
        if connection_type == "http":
            print(f"🌐 HTTP服务无需启动本地进程: {server_name}")
            return True
        
        # 检查command字段
        if "command" not in server_config:
            print(f"❌ 服务器配置缺少command字段: {server_name}")
            return False
        
        # 如果服务已经在运行，先停止它
        if server_name in self.server_processes:
            self.stop_server(server_name)
        
        try:
            # 构建命令
            command = [server_config["command"]] + server_config.get("args", [])
            print(f"🚀 启动服务: {server_name}")
            print(f"   命令: {' '.join(command)}")
            
            # 设置环境变量
            env = os.environ.copy()
            if server_config.get("env"):
                env.update(server_config["env"])
            
            # 启动进程
            process = subprocess.Popen(
                command,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )
            
            # 存储进程
            self.server_processes[server_name] = process
            
            # 等待一下确保启动成功
            time.sleep(1)
            
            # 检查进程状态
            if process.poll() is None:
                print(f"✅ 服务启动成功: {server_name} (PID: {process.pid})")
                return True
            else:
                print(f"❌ 服务启动失败: {server_name} (返回码: {process.returncode})")
                # 读取错误输出
                stdout, stderr = process.communicate()
                if stderr:
                    print(f"   错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动服务失败: {server_name}, 错误: {e}")
            return False
    
    def stop_server(self, server_name):
        """停止服务器"""
        if server_name in self.server_processes:
            try:
                process = self.server_processes[server_name]
                print(f"🛑 停止服务: {server_name}")
                
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    print(f"   强制终止服务: {server_name}")
                
                del self.server_processes[server_name]
                print(f"✅ 服务已停止: {server_name}")
                return True
            except Exception as e:
                print(f"❌ 停止服务失败: {server_name}, 错误: {e}")
                return False
        return True
    
    def get_server_status(self, server_name):
        """获取服务器状态"""
        server_config = self.get_all_servers().get(server_name)
        if not server_config:
            return "not_found"
        
        if not server_config.get("enabled", False):
            return "disabled"
        
        connection_type = server_config.get("connection_type", "stdio")
        if connection_type == "http":
            return "running"
        
        if server_name in self.server_processes:
            process = self.server_processes[server_name]
            if process.poll() is None:
                return "running"
            else:
                # 进程已结束，清理
                del self.server_processes[server_name]
                return "stopped"
        
        return "stopped"
    
    def start_all_enabled_servers(self):
        """启动所有启用的服务"""
        results = {}
        servers = self.get_all_servers()
        
        for server_name, server_config in servers.items():
            if server_config.get("enabled", False):
                results[server_name] = self.start_server(server_name)
        
        return results
    
    def show_status(self):
        """显示所有服务状态"""
        print("\n=== MCP服务状态 ===")
        servers = self.get_all_servers()
        
        for server_name, server_config in servers.items():
            status = self.get_server_status(server_name)
            enabled = server_config.get("enabled", False)
            connection_type = server_config.get("connection_type", "stdio")
            
            status_icon = {
                "running": "🟢",
                "stopped": "🔴",
                "disabled": "⚪",
                "not_found": "❓"
            }.get(status, "❓")
            
            status_text = {
                "running": "运行中",
                "stopped": "已停止",
                "disabled": "已禁用",
                "not_found": "未找到"
            }.get(status, "未知")
            
            print(f"{status_icon} {server_name} - {status_text} (enabled: {enabled}, type: {connection_type})")


def main():
    """主函数"""
    print("=== MCP服务管理器 ===")
    
    manager = SimpleMCPManager()
    
    # 显示当前状态
    manager.show_status()
    
    # 启动所有启用的服务
    print("\n=== 启动所有启用的服务 ===")
    results = manager.start_all_enabled_servers()
    
    enabled_count = sum(1 for server in manager.get_all_servers().values() if server.get("enabled", False))
    success_count = sum(1 for result in results.values() if result)
    
    print(f"\n启动结果: {success_count}/{enabled_count} 个服务启动成功")
    
    # 显示最终状态
    manager.show_status()
    
    # 等待用户输入
    input("\n按回车键停止所有服务...")
    
    # 停止所有服务
    print("\n=== 停止所有服务 ===")
    for server_name in list(manager.server_processes.keys()):
        manager.stop_server(server_name)


if __name__ == "__main__":
    main()
