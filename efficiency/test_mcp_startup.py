#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCP服务启动
"""

import json
import os
import subprocess
import sys
import time


def test_npx_command():
    """测试npx命令是否可用"""
    print("=== 测试npx命令 ===")
    try:
        result = subprocess.run(["npx", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ npx可用，版本: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ npx命令失败: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ npx命令不存在")
        return False
    except Exception as e:
        print(f"❌ npx测试失败: {e}")
        return False


def test_mcp_server_command():
    """测试MCP服务器命令"""
    print("\n=== 测试MCP服务器命令 ===")

    # 测试files服务命令（使用当前目录）
    command = ["npx", "-y", "@modelcontextprotocol/server-filesystem", "."]
    print(f"测试命令: {' '.join(command)}")

    try:
        # 启动进程
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        print(f"进程已启动，PID: {process.pid}")

        # 等待一段时间看是否正常运行
        time.sleep(3)

        # 检查进程状态
        poll_result = process.poll()
        if poll_result is None:
            print("✅ 进程正在运行")

            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 进程已正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 进程被强制终止")

        else:
            print(f"❌ 进程已退出，返回码: {poll_result}")

            # 读取错误输出
            stdout, stderr = process.communicate()
            if stdout:
                print(f"标准输出: {stdout}")
            if stderr:
                print(f"错误输出: {stderr}")

    except Exception as e:
        print(f"❌ 启动进程失败: {e}")


def test_directory_access():
    """测试目录访问"""
    print("\n=== 测试目录访问 ===")

    # 测试当前目录
    test_dir = "."
    print(f"测试目录: {os.path.abspath(test_dir)}")

    if os.path.exists(test_dir):
        print("✅ 目录存在")
        if os.access(test_dir, os.R_OK):
            print("✅ 目录可读")
        else:
            print("❌ 目录不可读")

        if os.access(test_dir, os.W_OK):
            print("✅ 目录可写")
        else:
            print("❌ 目录不可写")
    else:
        print("❌ 目录不存在")


def analyze_config():
    """分析配置"""
    print("\n=== 分析配置 ===")

    config_path = "config/mcp_config.json"
    if not os.path.exists(config_path):
        print("❌ 配置文件不存在")
        return

    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)

    servers = config.get("mcpServers", {})
    enabled_servers = {name: cfg for name, cfg in servers.items() if cfg.get("enabled", False)}

    print(f"总服务数: {len(servers)}")
    print(f"启用服务数: {len(enabled_servers)}")

    for name, cfg in enabled_servers.items():
        connection_type = cfg.get("connection_type", "stdio")
        print(f"\n启用的服务: {name}")
        print(f"  连接类型: {connection_type}")

        if connection_type == "stdio" and "command" in cfg:
            command = cfg["command"]
            args = cfg.get("args", [])
            full_command = [command] + args
            print(f"  完整命令: {' '.join(full_command)}")


if __name__ == "__main__":
    analyze_config()
    test_directory_access()
    test_npx_command()
    test_mcp_server_command()
