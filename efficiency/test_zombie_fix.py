#!/usr/bin/env python3
"""
测试僵尸进程修复
"""
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append('/app')

from functions.mcp_server import MCPServerManager

def main():
    """主函数"""
    print("=== 测试僵尸进程修复 ===")
    
    # 1. 检查当前PID文件中的进程状态
    print("\n1. 检查当前PID文件:")
    pid_dir = "config/mcp_pids"
    if os.path.exists(pid_dir):
        pid_files = os.listdir(pid_dir)
        for pid_file in pid_files:
            if pid_file.endswith('.pid'):
                pid_path = os.path.join(pid_dir, pid_file)
                try:
                    with open(pid_path, 'r') as f:
                        pid = int(f.read().strip())
                    
                    print(f"   📄 {pid_file}: PID={pid}")
                    
                    # 检查进程状态
                    try:
                        # 使用ps命令检查
                        import subprocess
                        result = subprocess.run(
                            ["ps", "-p", str(pid), "-o", "pid,stat,cmd", "--no-headers"],
                            capture_output=True,
                            text=True,
                            timeout=5
                        )
                        
                        if result.returncode == 0:
                            output = result.stdout.strip()
                            print(f"     ps输出: {output}")
                            if '<defunct>' in output or 'Z' in output:
                                print(f"     ❌ 僵尸进程")
                            else:
                                print(f"     ✅ 正常进程")
                        else:
                            print(f"     ❌ 进程不存在")
                    except Exception as e:
                        print(f"     ❌ 检查失败: {e}")
                    
                    # 测试新的_is_process_running方法
                    manager = MCPServerManager()
                    is_running = manager._is_process_running(pid)
                    print(f"     新方法检查结果: {'运行中' if is_running else '未运行'}")
                    
                except Exception as e:
                    print(f"     ❌ 读取PID文件失败: {e}")
    
    # 2. 测试服务状态
    print("\n2. 测试服务状态:")
    try:
        manager = MCPServerManager()
        
        # 检查files服务状态
        status = manager.get_server_status('files')
        print(f"   files服务状态: {status}")
        
        # 获取详细日志
        logs = manager.get_server_logs('files')
        print(f"   日志状态: {logs.get('status', '未知')}")
        
        # 如果状态不正确，尝试清理和重启
        if status != 'running':
            print("\n3. 尝试清理和重启:")
            
            # 停止服务（清理僵尸进程）
            print("   🛑 停止服务...")
            stop_result = manager.stop_server('files')
            print(f"   停止结果: {stop_result}")
            
            # 等待一下
            time.sleep(1)
            
            # 重新启动
            print("   🚀 重新启动服务...")
            start_result = manager.start_server('files')
            print(f"   启动结果: {start_result}")
            
            # 等待一下
            time.sleep(2)
            
            # 检查新状态
            new_status = manager.get_server_status('files')
            print(f"   新状态: {new_status}")
            
            # 检查进程是否真的在运行
            new_logs = manager.get_server_logs('files')
            print(f"   新日志状态: {new_logs.get('status', '未知')}")
            
            # 显示最新日志
            recent_stdout = new_logs.get('stdout', [])[-3:]
            if recent_stdout:
                print("   最新日志:")
                for log in recent_stdout:
                    print(f"     {log}")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
