#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态修复功能
"""

import os
import sys
import time
import subprocess

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_status_fix():
    """测试状态修复功能"""
    print("=== 测试MCP服务状态修复 ===\n")
    
    # 1. 手动启动一个MCP服务
    print("1. 手动启动files服务...")
    command = ["npx", "-y", "@modelcontextprotocol/server-filesystem", "."]
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    print(f"   启动命令: {' '.join(command)}")
    print(f"   进程PID: {process.pid}")
    
    # 等待一下确保启动成功
    time.sleep(2)
    
    if process.poll() is None:
        print("   ✅ 服务启动成功")
    else:
        print("   ❌ 服务启动失败")
        return
    
    # 2. 手动创建PID文件
    print("\n2. 创建PID文件...")
    pid_dir = "config/mcp_pids"
    os.makedirs(pid_dir, exist_ok=True)
    
    pid_file = os.path.join(pid_dir, "files.pid")
    with open(pid_file, 'w') as f:
        f.write(str(process.pid))
    print(f"   PID文件: {pid_file} -> {process.pid}")
    
    # 3. 测试状态检测（模拟Streamlit重启后的状态检测）
    print("\n3. 模拟Streamlit重启，测试状态检测...")
    
    try:
        # 重新导入模块（模拟Streamlit重启）
        if 'functions.mcp_server' in sys.modules:
            del sys.modules['functions.mcp_server']
        
        from functions.mcp_server import mcp_server_manager
        
        # 检查服务状态
        status = mcp_server_manager.get_server_status("files")
        print(f"   get_server_status: {status}")
        
        # 检查服务日志状态
        logs = mcp_server_manager.get_server_logs("files")
        log_status = logs.get("status", "未知")
        print(f"   get_server_logs.status: {log_status}")
        
        # 验证结果
        if status == "running" and log_status == "运行中":
            print("   ✅ 状态检测成功！服务状态正确显示为运行中")
        else:
            print(f"   ❌ 状态检测失败！status={status}, log_status={log_status}")
        
        # 显示日志内容
        stdout_logs = logs.get("stdout", [])
        if stdout_logs:
            print("   📤 标准输出:")
            for log_line in stdout_logs[-3:]:  # 显示最近3条
                print(f"      {log_line}")
        
    except Exception as e:
        print(f"   ❌ 导入或测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 4. 清理
    print("\n4. 清理...")
    try:
        process.terminate()
        process.wait(timeout=5)
        print("   ✅ 进程已终止")
    except subprocess.TimeoutExpired:
        process.kill()
        print("   ⚠️ 进程被强制终止")
    except Exception as e:
        print(f"   ❌ 终止进程失败: {e}")
    
    # 删除PID文件
    try:
        if os.path.exists(pid_file):
            os.remove(pid_file)
            print("   ✅ PID文件已删除")
    except Exception as e:
        print(f"   ❌ 删除PID文件失败: {e}")


def test_real_scenario():
    """测试真实场景"""
    print("\n=== 测试真实场景 ===\n")
    
    try:
        from functions.mcp_server import mcp_server_manager
        
        print("1. 当前服务状态:")
        servers = mcp_server_manager.get_all_servers()
        for server_name, server_config in servers.items():
            if server_config.get("enabled", False):
                status = mcp_server_manager.get_server_status(server_name)
                logs = mcp_server_manager.get_server_logs(server_name)
                log_status = logs.get("status", "未知")
                
                print(f"   {server_name}:")
                print(f"     get_server_status: {status}")
                print(f"     get_server_logs.status: {log_status}")
                
                # 检查PID文件
                pid_file = os.path.join("config/mcp_pids", f"{server_name}.pid")
                if os.path.exists(pid_file):
                    with open(pid_file, 'r') as f:
                        pid = f.read().strip()
                    print(f"     PID文件: {pid}")
                    
                    # 检查进程是否还在运行
                    try:
                        os.kill(int(pid), 0)
                        print(f"     进程状态: 运行中")
                    except (OSError, ProcessLookupError):
                        print(f"     进程状态: 已结束")
                else:
                    print(f"     PID文件: 不存在")
        
        print("\n2. 尝试启动files服务:")
        success = mcp_server_manager.start_server("files")
        if success:
            print("   ✅ 启动成功")
            
            # 再次检查状态
            status = mcp_server_manager.get_server_status("files")
            logs = mcp_server_manager.get_server_logs("files")
            log_status = logs.get("status", "未知")
            
            print(f"   启动后状态:")
            print(f"     get_server_status: {status}")
            print(f"     get_server_logs.status: {log_status}")
        else:
            print("   ❌ 启动失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_status_fix()
    test_real_scenario()
