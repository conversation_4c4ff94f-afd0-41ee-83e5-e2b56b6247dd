#!/usr/bin/env python3
"""
最终测试中国铁路MCP服务器 - 使用有效日期
"""
import sys
import asyncio
import json
import base64
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append('/app')

from functions.mcp_server import MCPServerManager

# 设置日志
logging.basicConfig(level=logging.INFO)

async def test_chinarailway_with_valid_dates():
    """使用有效日期测试中国铁路搜索功能"""
    print("=== 使用有效日期测试中国铁路MCP服务器 ===")
    
    try:
        # 导入MCP相关模块
        import mcp
        from mcp.client.streamable_http import streamablehttp_client
        
        # 获取服务配置
        manager = MCPServerManager()
        server_config = manager.get_server_config('chinarailway')
        
        # 构建连接URL
        config = server_config.get('config', {})
        config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
        api_key = server_config.get('api_key', '')
        server_name = server_config.get('server_name', '')
        
        url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"
        
        # 连接到服务器
        async with streamablehttp_client(url) as (read_stream, write_stream, _):
            async with mcp.ClientSession(read_stream, write_stream) as session:
                # 初始化连接
                await session.initialize()
                print("✅ 连接初始化成功")
                
                # 生成有效的日期（今天+1到今天+7天）
                today = datetime.now()
                test_cases = [
                    {
                        "name": "北京到上海（明天）",
                        "params": {
                            "fromCity": "北京",
                            "toCity": "上海", 
                            "date": (today + timedelta(days=1)).strftime("%Y-%m-%d")
                        }
                    },
                    {
                        "name": "广州到深圳（后天）",
                        "params": {
                            "fromCity": "广州",
                            "toCity": "深圳",
                            "date": (today + timedelta(days=2)).strftime("%Y-%m-%d")
                        }
                    },
                    {
                        "name": "上海到杭州（3天后）",
                        "params": {
                            "fromCity": "上海",
                            "toCity": "杭州",
                            "date": (today + timedelta(days=3)).strftime("%Y-%m-%d")
                        }
                    }
                ]
                
                for i, test_case in enumerate(test_cases, 1):
                    print(f"\n{i}. 测试 {test_case['name']}:")
                    print(f"   参数: {test_case['params']}")
                    
                    try:
                        # 调用搜索工具
                        result = await session.call_tool("search", test_case['params'])
                        
                        print("   ✅ 搜索成功")
                        
                        # 解析结果
                        if hasattr(result, 'content'):
                            content = result.content
                            if isinstance(content, list) and len(content) > 0:
                                # 显示第一个内容项
                                first_content = content[0]
                                if hasattr(first_content, 'text'):
                                    text = first_content.text
                                    
                                    # 解析火车票信息
                                    print(f"   📄 搜索结果:")
                                    
                                    # 如果结果很长，尝试解析JSON或格式化显示
                                    if len(text) > 1000:
                                        # 尝试解析为JSON
                                        try:
                                            import re
                                            # 查找火车班次信息
                                            lines = text.split('\n')
                                            train_count = 0
                                            for line in lines[:20]:  # 只显示前20行
                                                if line.strip():
                                                    print(f"      {line.strip()}")
                                                    if any(keyword in line for keyword in ['G', 'D', 'C', 'T', 'K']):
                                                        train_count += 1
                                            
                                            if train_count > 0:
                                                print(f"   🚄 找到约 {train_count} 个车次信息")
                                            else:
                                                print(f"   📝 完整结果长度: {len(text)} 字符")
                                                
                                        except Exception as parse_error:
                                            print(f"   📝 结果长度: {len(text)} 字符")
                                            print(f"   📄 前500字符: {text[:500]}...")
                                    else:
                                        print(f"      {text}")
                                else:
                                    print(f"   📄 搜索结果: {str(first_content)[:500]}...")
                            else:
                                print(f"   📄 搜索结果: {str(content)[:500]}...")
                        else:
                            print(f"   📄 搜索结果: {str(result)[:500]}...")
                            
                    except Exception as e:
                        print(f"   ❌ 搜索失败: {e}")
                        
                    # 添加延迟避免请求过快
                    await asyncio.sleep(2)
                
                print("\n✅ 所有测试完成")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        asyncio.run(test_chinarailway_with_valid_dates())
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
