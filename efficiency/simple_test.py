#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试
"""

import os
import sys

print("=== 简单测试 ===")

# 检查当前目录
print(f"当前目录: {os.getcwd()}")

# 检查文件是否存在
mcp_server_file = "functions/mcp_server.py"
print(f"MCP服务器文件存在: {os.path.exists(mcp_server_file)}")

# 检查配置文件
config_file = "config/mcp_config.json"
print(f"配置文件存在: {os.path.exists(config_file)}")

# 检查PID目录
pid_dir = "config/mcp_pids"
print(f"PID目录存在: {os.path.exists(pid_dir)}")

if os.path.exists(pid_dir):
    pid_files = os.listdir(pid_dir)
    print(f"PID文件: {pid_files}")

# 尝试导入
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    print("尝试导入MCP服务器管理器...")
    from functions.mcp_server import mcp_server_manager
    print("✅ 导入成功")
    
    # 检查方法是否存在
    print(f"_load_pid方法存在: {hasattr(mcp_server_manager, '_load_pid')}")
    print(f"_is_process_running方法存在: {hasattr(mcp_server_manager, '_is_process_running')}")
    
    # 测试基本功能
    servers = mcp_server_manager.get_all_servers()
    print(f"服务数量: {len(servers)}")
    
    for server_name, server_config in servers.items():
        enabled = server_config.get("enabled", False)
        if enabled:
            print(f"\n测试服务: {server_name}")
            status = mcp_server_manager.get_server_status(server_name)
            print(f"  状态: {status}")
            
            logs = mcp_server_manager.get_server_logs(server_name)
            log_status = logs.get("status", "未知")
            print(f"  日志状态: {log_status}")
            
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===")
