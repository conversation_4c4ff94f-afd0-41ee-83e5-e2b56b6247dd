#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复
"""

import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mcp_fix():
    """测试MCP修复"""
    print("=== 测试MCP最终修复 ===\n")
    
    try:
        # 导入MCP服务器管理器
        from functions.mcp_server import mcp_server_manager
        print("✅ 成功导入MCP服务器管理器")
        
        # 1. 检查当前状态
        print("\n1. 检查当前状态:")
        servers = mcp_server_manager.get_all_servers()
        for server_name, server_config in servers.items():
            if server_config.get("enabled", False):
                status = mcp_server_manager.get_server_status(server_name)
                logs = mcp_server_manager.get_server_logs(server_name)
                log_status = logs.get("status", "未知")
                
                print(f"   {server_name}:")
                print(f"     配置状态: {'启用' if server_config.get('enabled') else '禁用'}")
                print(f"     get_server_status: {status}")
                print(f"     get_server_logs.status: {log_status}")
        
        # 2. 启动files服务
        print("\n2. 启动files服务:")
        success = mcp_server_manager.start_server("files")
        print(f"   启动结果: {'成功' if success else '失败'}")
        
        if success:
            # 检查启动后的状态
            print("\n3. 检查启动后状态:")
            status = mcp_server_manager.get_server_status("files")
            logs = mcp_server_manager.get_server_logs("files")
            log_status = logs.get("status", "未知")
            
            print(f"   get_server_status: {status}")
            print(f"   get_server_logs.status: {log_status}")
            
            # 显示日志
            stdout_logs = logs.get("stdout", [])
            stderr_logs = logs.get("stderr", [])
            
            if stdout_logs:
                print("   📤 标准输出:")
                for log_line in stdout_logs[-5:]:  # 显示最近5条
                    print(f"      {log_line}")
            
            if stderr_logs:
                print("   ❌ 错误输出:")
                for log_line in stderr_logs[-5:]:  # 显示最近5条
                    print(f"      {log_line}")
            
            # 检查PID文件
            pid_file = "config/mcp_pids/files.pid"
            if os.path.exists(pid_file):
                with open(pid_file, 'r') as f:
                    pid = f.read().strip()
                print(f"   PID文件: {pid}")
                
                # 检查进程是否存在
                try:
                    os.kill(int(pid), 0)
                    print(f"   进程状态: 运行中")
                except (OSError, ProcessLookupError, ValueError):
                    print(f"   进程状态: 已结束")
            else:
                print("   PID文件: 不存在")
            
            # 4. 模拟页面刷新（重新创建管理器实例）
            print("\n4. 模拟页面刷新:")
            
            # 删除模块缓存
            if 'functions.mcp_server' in sys.modules:
                del sys.modules['functions.mcp_server']
            
            # 重新导入
            from functions.mcp_server import mcp_server_manager as new_manager
            
            # 检查状态恢复
            status_after = new_manager.get_server_status("files")
            logs_after = new_manager.get_server_logs("files")
            log_status_after = logs_after.get("status", "未知")
            
            print(f"   刷新后 get_server_status: {status_after}")
            print(f"   刷新后 get_server_logs.status: {log_status_after}")
            
            # 显示恢复日志
            stdout_logs_after = logs_after.get("stdout", [])
            if stdout_logs_after:
                print("   📤 恢复后日志:")
                for log_line in stdout_logs_after[-3:]:  # 显示最近3条
                    print(f"      {log_line}")
            
            # 验证结果
            if status_after == "running" and log_status_after == "运行中":
                print("   ✅ 状态恢复成功！")
            else:
                print(f"   ❌ 状态恢复失败: {status_after} vs {log_status_after}")
        
        else:
            print("   ❌ 服务启动失败，无法进行后续测试")
            
            # 显示启动失败的日志
            logs = mcp_server_manager.get_server_logs("files")
            stderr_logs = logs.get("stderr", [])
            
            if stderr_logs:
                print("   ❌ 启动失败原因:")
                for log_line in stderr_logs[-3:]:
                    print(f"      {log_line}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mcp_fix()
