#!/usr/bin/env python3
"""
测试中国铁路MCP服务器
"""
import sys
import asyncio
import json
import base64
import logging

# 添加项目根目录到Python路径
sys.path.append('/app')

from functions.mcp_server import MCPServerManager

# 设置日志
logging.basicConfig(level=logging.INFO)

async def test_chinarailway_mcp():
    """测试中国铁路MCP服务器"""
    print("=== 测试中国铁路MCP服务器 ===")
    
    # 1. 检查服务状态
    print("\n1. 检查服务状态:")
    manager = MCPServerManager()
    
    # 检查chinarailway服务状态
    status = manager.get_server_status('chinarailway')
    print(f"   chinarailway服务状态: {status}")
    
    if status != 'running':
        print("   ❌ 服务未运行，无法进行测试")
        return
    
    # 2. 测试MCP连接
    print("\n2. 测试MCP连接:")
    
    try:
        # 导入MCP相关模块
        import mcp
        from mcp.client.streamable_http import streamablehttp_client
        
        # 获取服务配置
        server_config = manager.get_server_config('chinarailway')
        if not server_config:
            print("   ❌ 无法获取服务配置")
            return
        
        # 构建连接URL
        config = server_config.get('config', {})
        config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
        api_key = server_config.get('api_key', '')
        server_name = server_config.get('server_name', '')
        
        url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"
        print(f"   连接URL: {url[:80]}...")
        
        # 连接到服务器
        print("   🔗 正在连接到MCP服务器...")
        async with streamablehttp_client(url) as (read_stream, write_stream, _):
            async with mcp.ClientSession(read_stream, write_stream) as session:
                # 初始化连接
                print("   🚀 初始化连接...")
                await session.initialize()
                print("   ✅ 连接初始化成功")
                
                # 3. 列出可用工具
                print("\n3. 列出可用工具:")
                tools_result = await session.list_tools()
                tools = tools_result.tools
                
                print(f"   📋 可用工具数量: {len(tools)}")
                for i, tool in enumerate(tools, 1):
                    print(f"   {i}. {tool.name}")
                    if hasattr(tool, 'description') and tool.description:
                        print(f"      描述: {tool.description}")
                
                # 4. 测试工具调用
                if tools:
                    print("\n4. 测试工具调用:")
                    
                    # 选择第一个工具进行测试
                    test_tool = tools[0]
                    print(f"   🧪 测试工具: {test_tool.name}")
                    
                    try:
                        # 根据工具名称准备测试参数
                        test_args = {}
                        
                        if 'search' in test_tool.name.lower() or 'query' in test_tool.name.lower():
                            # 如果是搜索类工具，提供搜索参数
                            test_args = {
                                "from": "北京",
                                "to": "上海",
                                "date": "2024-12-01"
                            }
                        elif 'station' in test_tool.name.lower():
                            # 如果是车站查询工具
                            test_args = {
                                "name": "北京"
                            }
                        
                        print(f"   📤 调用参数: {test_args}")
                        
                        # 调用工具
                        result = await session.call_tool(test_tool.name, test_args)
                        
                        print("   ✅ 工具调用成功")
                        print(f"   📥 返回结果类型: {type(result)}")
                        
                        # 显示结果内容（限制长度）
                        if hasattr(result, 'content'):
                            content_str = str(result.content)
                            if len(content_str) > 500:
                                content_str = content_str[:500] + "..."
                            print(f"   📄 结果内容: {content_str}")
                        else:
                            result_str = str(result)
                            if len(result_str) > 500:
                                result_str = result_str[:500] + "..."
                            print(f"   📄 结果: {result_str}")
                            
                    except Exception as e:
                        print(f"   ❌ 工具调用失败: {e}")
                        
                        # 尝试不带参数调用
                        try:
                            print("   🔄 尝试无参数调用...")
                            result = await session.call_tool(test_tool.name, {})
                            print("   ✅ 无参数调用成功")
                            print(f"   📥 返回结果: {str(result)[:200]}...")
                        except Exception as e2:
                            print(f"   ❌ 无参数调用也失败: {e2}")
                
                # 5. 列出资源（如果支持）
                print("\n5. 检查资源:")
                try:
                    resources_result = await session.list_resources()
                    resources = resources_result.resources
                    print(f"   📚 可用资源数量: {len(resources)}")
                    for i, resource in enumerate(resources, 1):
                        print(f"   {i}. {resource.name}")
                        if hasattr(resource, 'description') and resource.description:
                            print(f"      描述: {resource.description}")
                except Exception as e:
                    print(f"   ℹ️ 资源列表不可用: {e}")
                
                print("\n✅ 测试完成")
                
    except ImportError as e:
        print(f"   ❌ MCP模块导入失败: {e}")
        print("   💡 请确保已安装MCP SDK: pip install mcp")
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        asyncio.run(test_chinarailway_mcp())
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
