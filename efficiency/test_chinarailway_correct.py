#!/usr/bin/env python3
"""
测试中国铁路MCP服务器 - 使用正确参数
"""
import sys
import asyncio
import json
import base64
import logging

# 添加项目根目录到Python路径
sys.path.append('/app')

from functions.mcp_server import MCPServerManager

# 设置日志
logging.basicConfig(level=logging.INFO)

async def test_chinarailway_search():
    """测试中国铁路搜索功能"""
    print("=== 测试中国铁路MCP服务器搜索功能 ===")
    
    try:
        # 导入MCP相关模块
        import mcp
        from mcp.client.streamable_http import streamablehttp_client
        
        # 获取服务配置
        manager = MCPServerManager()
        server_config = manager.get_server_config('chinarailway')
        
        # 构建连接URL
        config = server_config.get('config', {})
        config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
        api_key = server_config.get('api_key', '')
        server_name = server_config.get('server_name', '')
        
        url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"
        
        # 连接到服务器
        async with streamablehttp_client(url) as (read_stream, write_stream, _):
            async with mcp.ClientSession(read_stream, write_stream) as session:
                # 初始化连接
                await session.initialize()
                print("✅ 连接初始化成功")
                
                # 测试不同的搜索参数
                test_cases = [
                    {
                        "name": "北京到上海",
                        "params": {
                            "fromCity": "北京",
                            "toCity": "上海", 
                            "date": "2024-12-01"
                        }
                    },
                    {
                        "name": "广州到深圳",
                        "params": {
                            "fromCity": "广州",
                            "toCity": "深圳",
                            "date": "2024-12-02"
                        }
                    },
                    {
                        "name": "杭州到南京",
                        "params": {
                            "fromCity": "杭州",
                            "toCity": "南京",
                            "date": "2024-12-03"
                        }
                    }
                ]
                
                for i, test_case in enumerate(test_cases, 1):
                    print(f"\n{i}. 测试 {test_case['name']}:")
                    print(f"   参数: {test_case['params']}")
                    
                    try:
                        # 调用搜索工具
                        result = await session.call_tool("search", test_case['params'])
                        
                        print("   ✅ 搜索成功")
                        
                        # 解析结果
                        if hasattr(result, 'content'):
                            content = result.content
                            if isinstance(content, list) and len(content) > 0:
                                # 显示第一个内容项
                                first_content = content[0]
                                if hasattr(first_content, 'text'):
                                    text = first_content.text
                                    # 限制显示长度
                                    if len(text) > 800:
                                        text = text[:800] + "..."
                                    print(f"   📄 搜索结果:\n{text}")
                                else:
                                    print(f"   📄 搜索结果: {str(first_content)[:500]}...")
                            else:
                                print(f"   📄 搜索结果: {str(content)[:500]}...")
                        else:
                            print(f"   📄 搜索结果: {str(result)[:500]}...")
                            
                    except Exception as e:
                        print(f"   ❌ 搜索失败: {e}")
                        
                    # 添加延迟避免请求过快
                    await asyncio.sleep(1)
                
                print("\n✅ 所有测试完成")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_tool_schema():
    """获取工具的详细schema信息"""
    print("\n=== 获取工具Schema信息 ===")
    
    try:
        import mcp
        from mcp.client.streamable_http import streamablehttp_client
        
        manager = MCPServerManager()
        server_config = manager.get_server_config('chinarailway')
        
        config = server_config.get('config', {})
        config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
        api_key = server_config.get('api_key', '')
        server_name = server_config.get('server_name', '')
        
        url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"
        
        async with streamablehttp_client(url) as (read_stream, write_stream, _):
            async with mcp.ClientSession(read_stream, write_stream) as session:
                await session.initialize()
                
                # 获取工具列表
                tools_result = await session.list_tools()
                
                for tool in tools_result.tools:
                    print(f"\n工具名称: {tool.name}")
                    print(f"描述: {tool.description}")
                    
                    if hasattr(tool, 'inputSchema'):
                        print(f"输入Schema: {json.dumps(tool.inputSchema, indent=2, ensure_ascii=False)}")
                    
    except Exception as e:
        print(f"❌ 获取Schema失败: {e}")

def main():
    """主函数"""
    try:
        # 先获取schema信息
        asyncio.run(test_tool_schema())
        
        # 然后进行搜索测试
        asyncio.run(test_chinarailway_search())
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
