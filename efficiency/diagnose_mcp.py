#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断MCP服务状态问题
"""

import os
import subprocess
import json

def diagnose_mcp():
    """诊断MCP服务状态"""
    print("=== MCP服务状态诊断 ===\n")
    
    # 1. 检查配置文件
    print("1. 检查配置文件:")
    config_path = "config/mcp_config.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        servers = config.get("mcpServers", {})
        print(f"   总服务数: {len(servers)}")
        
        for name, cfg in servers.items():
            enabled = cfg.get("enabled", False)
            connection_type = cfg.get("connection_type", "stdio")
            print(f"   {name}: enabled={enabled}, type={connection_type}")
            
            if enabled and connection_type == "stdio":
                command = cfg.get("command", "")
                args = cfg.get("args", [])
                print(f"     命令: {command} {' '.join(args)}")
    else:
        print("   ❌ 配置文件不存在")
    
    # 2. 检查PID文件目录
    print("\n2. 检查PID文件目录:")
    pid_dir = "config/mcp_pids"
    if os.path.exists(pid_dir):
        pid_files = os.listdir(pid_dir)
        print(f"   PID文件: {pid_files}")
        
        for pid_file in pid_files:
            if pid_file.endswith('.pid'):
                pid_path = os.path.join(pid_dir, pid_file)
                try:
                    with open(pid_path, 'r') as f:
                        pid = f.read().strip()
                    print(f"   {pid_file}: {pid}")
                    
                    # 检查进程是否存在
                    try:
                        os.kill(int(pid), 0)
                        print(f"     ✅ 进程存在")
                    except (OSError, ProcessLookupError, ValueError):
                        print(f"     ❌ 进程不存在")
                except Exception as e:
                    print(f"     ❌ 读取失败: {e}")
    else:
        print("   ❌ PID目录不存在")
    
    # 3. 检查当前运行的进程
    print("\n3. 检查当前运行的进程:")
    
    # 检查npx进程
    try:
        result = subprocess.run(
            ["ps", "aux"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            npx_processes = [line for line in lines if 'npx' in line and 'grep' not in line]
            mcp_processes = [line for line in lines if 'mcp' in line.lower() and 'grep' not in line]
            filesystem_processes = [line for line in lines if 'filesystem' in line and 'grep' not in line]
            
            print(f"   NPX进程数: {len(npx_processes)}")
            for proc in npx_processes:
                print(f"     {proc.strip()}")
            
            print(f"   MCP进程数: {len(mcp_processes)}")
            for proc in mcp_processes:
                print(f"     {proc.strip()}")
                
            print(f"   Filesystem进程数: {len(filesystem_processes)}")
            for proc in filesystem_processes:
                print(f"     {proc.strip()}")
        else:
            print("   ❌ ps命令失败")
            
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
    
    # 4. 测试pgrep命令
    print("\n4. 测试pgrep命令:")
    
    search_patterns = [
        "@modelcontextprotocol/server-filesystem",
        "server-filesystem", 
        "npx"
    ]
    
    for pattern in search_patterns:
        try:
            result = subprocess.run(
                ["pgrep", "-f", pattern],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            print(f"   pgrep -f '{pattern}':")
            print(f"     返回码: {result.returncode}")
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                print(f"     找到PID: {pids}")
            else:
                print(f"     未找到匹配进程")
                
        except Exception as e:
            print(f"     ❌ pgrep失败: {e}")
    
    # 5. 手动启动测试
    print("\n5. 手动启动测试:")
    try:
        print("   尝试手动启动files服务...")
        command = ["npx", "-y", "@modelcontextprotocol/server-filesystem", "."]
        print(f"   命令: {' '.join(command)}")
        
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"   启动PID: {process.pid}")
        
        # 等待一下
        import time
        time.sleep(2)
        
        # 检查状态
        if process.poll() is None:
            print("   ✅ 进程仍在运行")
            
            # 测试pgrep
            try:
                result = subprocess.run(
                    ["pgrep", "-f", "@modelcontextprotocol/server-filesystem"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                if result.returncode == 0:
                    print(f"   ✅ pgrep找到进程: {result.stdout.strip()}")
                else:
                    print("   ❌ pgrep未找到进程")
            except Exception as e:
                print(f"   ❌ pgrep测试失败: {e}")
            
            # 终止进程
            process.terminate()
            process.wait(timeout=5)
            print("   ✅ 测试进程已终止")
        else:
            print(f"   ❌ 进程已退出，返回码: {process.returncode}")
            stdout, stderr = process.communicate()
            if stdout:
                print(f"   标准输出: {stdout}")
            if stderr:
                print(f"   错误输出: {stderr}")
                
    except Exception as e:
        print(f"   ❌ 手动启动测试失败: {e}")

if __name__ == "__main__":
    diagnose_mcp()
