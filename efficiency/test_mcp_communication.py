#!/usr/bin/env python3
"""
测试MCP服务器通信
"""
import subprocess
import json
import time
import threading

def test_mcp_server():
    """测试MCP服务器通信"""
    print("=== 测试MCP服务器通信 ===")
    
    # 启动MCP服务器
    print("启动MCP服务器...")
    process = subprocess.Popen(
        ["npx", "@modelcontextprotocol/server-filesystem", "."],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=0
    )
    
    print(f"进程PID: {process.pid}")
    
    def read_output():
        """读取输出的线程"""
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                print(f"输出: {line.strip()}")
        except Exception as e:
            print(f"读取输出错误: {e}")
    
    def read_error():
        """读取错误的线程"""
        try:
            while True:
                line = process.stderr.readline()
                if not line:
                    break
                print(f"错误: {line.strip()}")
        except Exception as e:
            print(f"读取错误输出错误: {e}")
    
    # 启动读取线程
    output_thread = threading.Thread(target=read_output, daemon=True)
    error_thread = threading.Thread(target=read_error, daemon=True)
    output_thread.start()
    error_thread.start()
    
    # 等待一下看进程是否立即退出
    time.sleep(2)
    
    if process.poll() is None:
        print("✅ 进程正在运行")
        
        # 发送MCP初始化消息
        print("发送初始化消息...")
        init_message = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {
                        "listChanged": True
                    }
                },
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        try:
            message_str = json.dumps(init_message) + "\n"
            print(f"发送消息: {message_str.strip()}")
            process.stdin.write(message_str)
            process.stdin.flush()
            
            # 等待响应
            time.sleep(2)
            
            # 检查进程状态
            if process.poll() is None:
                print("✅ 进程在初始化后仍在运行")
                
                # 发送关闭消息
                print("发送关闭消息...")
                shutdown_message = {
                    "jsonrpc": "2.0",
                    "method": "notifications/shutdown"
                }
                shutdown_str = json.dumps(shutdown_message) + "\n"
                process.stdin.write(shutdown_str)
                process.stdin.flush()
                
                # 等待进程退出
                try:
                    process.wait(timeout=5)
                    print(f"✅ 进程正常退出，返回码: {process.returncode}")
                except subprocess.TimeoutExpired:
                    print("⚠️ 进程未在5秒内退出，强制终止")
                    process.kill()
            else:
                print(f"❌ 进程在初始化后退出，返回码: {process.returncode}")
                
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            
    else:
        return_code = process.returncode
        print(f"❌ 进程立即退出，返回码: {return_code}")
        
        # 读取输出
        stdout, stderr = process.communicate()
        if stdout:
            print(f"标准输出: {stdout}")
        if stderr:
            print(f"错误输出: {stderr}")

def test_simple_startup():
    """测试简单启动"""
    print("\n=== 测试简单启动 ===")
    
    # 不重定向输出，让进程自然运行
    print("启动进程（不重定向输出）...")
    process = subprocess.Popen(
        ["npx", "@modelcontextprotocol/server-filesystem", "."]
    )
    
    print(f"进程PID: {process.pid}")
    
    # 等待一下
    time.sleep(3)
    
    if process.poll() is None:
        print("✅ 进程正在运行")
        
        # 检查进程状态
        try:
            import os
            os.kill(process.pid, 0)
            print("✅ 进程确实存在")
            
            # 检查是否是僵尸进程
            with open(f'/proc/{process.pid}/stat', 'r') as f:
                stat_line = f.read().strip()
                last_paren = stat_line.rfind(')')
                if last_paren != -1:
                    remaining = stat_line[last_paren + 1:].strip()
                    if remaining and remaining[0] == 'Z':
                        print("❌ 进程是僵尸进程")
                    else:
                        print("✅ 进程状态正常")
        except Exception as e:
            print(f"❌ 检查进程状态失败: {e}")
        
        # 终止进程
        print("终止进程...")
        process.terminate()
        try:
            process.wait(timeout=5)
            print(f"✅ 进程正常终止，返回码: {process.returncode}")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️ 强制终止进程")
    else:
        print(f"❌ 进程立即退出，返回码: {process.returncode}")

if __name__ == "__main__":
    test_mcp_server()
    test_simple_startup()
