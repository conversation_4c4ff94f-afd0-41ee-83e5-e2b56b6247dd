#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pgrep功能
"""

import subprocess
import time
import os

def test_pgrep():
    """测试pgrep命令"""
    print("=== 测试pgrep功能 ===\n")
    
    # 1. 启动一个测试进程
    print("1. 启动测试进程...")
    command = ["npx", "-y", "@modelcontextprotocol/server-filesystem", "."]
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    print(f"   命令: {' '.join(command)}")
    print(f"   PID: {process.pid}")
    
    # 等待进程启动
    time.sleep(2)
    
    if process.poll() is None:
        print("   ✅ 进程启动成功")
    else:
        print("   ❌ 进程启动失败")
        return
    
    # 2. 测试pgrep搜索
    print("\n2. 测试pgrep搜索...")
    
    search_patterns = [
        "@modelcontextprotocol/server-filesystem",
        "server-filesystem",
        "npx"
    ]
    
    for pattern in search_patterns:
        try:
            result = subprocess.run(
                ["pgrep", "-f", pattern],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            print(f"   搜索模式: {pattern}")
            print(f"   返回码: {result.returncode}")
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                print(f"   找到PID: {pids}")
                
                # 检查我们的进程是否在其中
                if str(process.pid) in pids:
                    print(f"   ✅ 找到我们的进程: {process.pid}")
                else:
                    print(f"   ⚠️ 未找到我们的进程: {process.pid}")
            else:
                print(f"   ❌ 未找到匹配进程")
                
        except Exception as e:
            print(f"   ❌ pgrep失败: {e}")
    
    # 3. 测试我们的检查函数
    print("\n3. 测试检查函数...")
    
    server_config = {
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-filesystem", "."]
    }
    
    try:
        # 模拟我们的检查逻辑
        command = server_config["command"]
        args = server_config.get("args", [])
        
        if command == "npx" and args:
            search_pattern = args[0] if args else command
        else:
            search_pattern = command
        
        print(f"   使用搜索模式: {search_pattern}")
        
        result = subprocess.run(
            ["pgrep", "-f", search_pattern],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            print(f"   ✅ 检查函数会返回True，找到PID: {pids}")
        else:
            print(f"   ❌ 检查函数会返回False")
            
    except Exception as e:
        print(f"   ❌ 检查函数测试失败: {e}")
    
    # 4. 清理
    print("\n4. 清理...")
    try:
        process.terminate()
        process.wait(timeout=5)
        print("   ✅ 进程已终止")
    except subprocess.TimeoutExpired:
        process.kill()
        print("   ⚠️ 进程被强制终止")
    except Exception as e:
        print(f"   ❌ 终止进程失败: {e}")

if __name__ == "__main__":
    test_pgrep()
