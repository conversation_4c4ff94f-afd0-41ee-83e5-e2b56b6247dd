"""
MCP服务端实现
"""
import os
import json
import logging
import asyncio
import subprocess
import time
from typing import Dict, Any, List, Optional, Tuple, Callable

# 导入MCP相关模块
from mcp import StdioServerParameters

MCP_AVAILABLE = True


class MCPServerManager:
    """MCP服务端管理器，负责管理MCP服务端进程"""

    def __init__(self, config_path: str = "config/mcp_config.json"):
        """初始化MCP服务端管理器

        Args:
            config_path: MCP配置文件路径
        """
        self.config_path = config_path
        self.pid_file_dir = "config/mcp_pids"  # PID文件目录
        self.server_processes = {}  # 存储运行中的MCP服务端进程
        self.server_logs = {}  # 存储服务日志
        self.config = self._load_config()

        # 创建PID文件目录
        os.makedirs(self.pid_file_dir, exist_ok=True)

        # 恢复之前运行的进程
        self._restore_processes()

    def _load_config(self) -> Dict[str, Any]:
        """加载MCP配置

        Returns:
            MCP配置
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            else:
                # 创建默认配置
                default_config = {"mcpServers": {}}
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logging.error(f"加载MCP配置失败: {e}")
            return {"mcpServers": {}}

    def _save_config(self, config: Dict[str, Any]) -> None:
        """保存MCP配置

        Args:
            config: MCP配置
        """
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存MCP配置失败: {e}")

    def _get_pid_file_path(self, server_name: str) -> str:
        """获取PID文件路径"""
        return os.path.join(self.pid_file_dir, f"{server_name}.pid")

    def _save_pid(self, server_name: str, pid: int) -> None:
        """保存进程PID到文件"""
        try:
            pid_file = self._get_pid_file_path(server_name)
            with open(pid_file, 'w') as f:
                f.write(str(pid))
        except Exception as e:
            logging.error(f"保存PID文件失败: {server_name}, {e}")

    def _load_pid(self, server_name: str) -> Optional[int]:
        """从文件加载进程PID"""
        try:
            pid_file = self._get_pid_file_path(server_name)
            if os.path.exists(pid_file):
                with open(pid_file, 'r') as f:
                    return int(f.read().strip())
        except Exception as e:
            logging.error(f"加载PID文件失败: {server_name}, {e}")
        return None

    def _remove_pid_file(self, server_name: str) -> None:
        """删除PID文件"""
        try:
            pid_file = self._get_pid_file_path(server_name)
            if os.path.exists(pid_file):
                os.remove(pid_file)
        except Exception as e:
            logging.error(f"删除PID文件失败: {server_name}, {e}")

    def _is_process_running(self, pid: int) -> bool:
        """检查进程是否还在运行"""
        try:
            # 发送信号0检查进程是否存在
            os.kill(pid, 0)

            # 进程存在，但需要检查是否是僵尸进程
            try:
                # 读取进程状态
                with open(f'/proc/{pid}/stat', 'r') as f:
                    stat_line = f.read().strip()
                    # /proc/pid/stat格式：pid (comm) state ...
                    # 进程名可能包含空格和括号，需要特殊处理
                    # 找到最后一个')'，状态字段在其后
                    last_paren = stat_line.rfind(')')
                    if last_paren != -1:
                        # 状态字段是')'后的第一个字段
                        remaining = stat_line[last_paren + 1:].strip()
                        if remaining and remaining[0] == 'Z':
                            logging.debug(f"进程 {pid} 是僵尸进程")
                            return False
                return True
            except (FileNotFoundError, IOError, IndexError):
                # 如果无法读取/proc/pid/stat，但os.kill成功，认为进程存在
                return True
        except (OSError, ProcessLookupError):
            return False

    def _check_process_by_command(self, server_config: Dict[str, Any]) -> bool:
        """通过命令检查进程是否在运行"""
        if not server_config or "command" not in server_config:
            logging.debug("服务配置无效或缺少command字段")
            return False

        try:
            command = server_config["command"]
            args = server_config.get("args", [])

            # 尝试多种搜索模式
            search_patterns = []

            if command == "npx" and args:
                # 对于npx命令，尝试多种搜索模式
                if args:
                    # 搜索包名
                    search_patterns.append(args[0])
                    # 搜索完整的第一个参数
                    if len(args) > 1:
                        search_patterns.append(f"{args[0]} {args[1]}")
                # 也搜索npx本身
                search_patterns.append("npx")
            else:
                search_patterns.append(command)

            logging.debug(f"尝试搜索模式: {search_patterns}")

            for pattern in search_patterns:
                try:
                    # 使用pgrep搜索进程
                    result = subprocess.run(
                        ["pgrep", "-f", pattern],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )

                    logging.debug(f"pgrep -f '{pattern}' 返回码: {result.returncode}")

                    if result.returncode == 0 and result.stdout.strip():
                        # 找到了匹配的进程
                        pids = result.stdout.strip().split('\n')
                        logging.info(f"找到匹配进程: {pattern}, PIDs: {pids}")

                        # 验证进程是否真的存在
                        for pid in pids:
                            try:
                                pid_int = int(pid.strip())
                                # 检查进程是否存在
                                os.kill(pid_int, 0)
                                logging.info(f"确认进程存在: PID {pid_int}")
                                return True
                            except (ValueError, OSError, ProcessLookupError):
                                logging.debug(f"进程不存在或无效: PID {pid}")
                                continue
                    else:
                        logging.debug(f"未找到匹配进程: {pattern}")

                except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
                    logging.debug(f"pgrep命令失败: {pattern}, {e}")
                    continue

        except Exception as e:
            logging.error(f"检查进程时出错: {e}")

        logging.debug("所有搜索模式都未找到匹配进程")
        return False

    def _restore_processes(self) -> None:
        """恢复之前运行的进程"""
        try:
            for server_name in self.get_all_servers():
                pid = self._load_pid(server_name)
                if pid and self._is_process_running(pid):
                    # 进程还在运行，恢复进程对象
                    try:
                        # 注意：这里我们无法完全恢复subprocess.Popen对象
                        # 但我们可以记录PID用于状态检查
                        logging.info(f"发现运行中的MCP服务: {server_name} (PID: {pid})")

                        # 创建一个简单的进程对象来跟踪PID
                        class ProcessTracker:
                            def __init__(self, pid):
                                self.pid = pid

                            def poll(self):
                                return None if self._is_process_running(self.pid) else 0

                            def terminate(self):
                                try:
                                    os.kill(self.pid, 15)  # SIGTERM
                                except (OSError, ProcessLookupError):
                                    pass

                            def kill(self):
                                try:
                                    os.kill(self.pid, 9)  # SIGKILL
                                except (OSError, ProcessLookupError):
                                    pass

                            def wait(self, timeout=None):
                                # 简化实现，实际中可能需要更复杂的等待逻辑
                                start_time = time.time()
                                while self._is_process_running(self.pid):
                                    if timeout and (time.time() - start_time) > timeout:
                                        raise subprocess.TimeoutExpired(None, timeout)
                                    time.sleep(0.1)
                                return 0

                            def _is_process_running(self, pid):
                                try:
                                    os.kill(pid, 0)
                                    # 检查是否是僵尸进程
                                    try:
                                        with open(f'/proc/{pid}/stat', 'r') as f:
                                            stat_line = f.read().strip()
                                            # 找到最后一个')'，状态字段在其后
                                            last_paren = stat_line.rfind(')')
                                            if last_paren != -1:
                                                remaining = stat_line[last_paren + 1:].strip()
                                                if remaining and remaining[0] == 'Z':
                                                    return False
                                        return True
                                    except (FileNotFoundError, IOError, IndexError):
                                        return True
                                except (OSError, ProcessLookupError):
                                    return False

                        self.server_processes[server_name] = ProcessTracker(pid)

                        # 恢复日志信息
                        if server_name not in self.server_logs:
                            self.server_logs[server_name] = {
                                "stdout": [f"[恢复] 发现运行中的服务，PID: {pid}"],
                                "stderr": [],
                                "command": "已恢复的进程",
                                "start_time": time.time()
                            }

                    except Exception as e:
                        logging.error(f"恢复进程失败: {server_name}, {e}")
                        self._remove_pid_file(server_name)
                else:
                    # 进程不存在，清理PID文件
                    if pid:
                        logging.info(f"清理无效的PID文件: {server_name}")
                        self._remove_pid_file(server_name)
        except Exception as e:
            logging.error(f"恢复进程时出错: {e}")

    def get_server_config(self, server_name: str) -> Optional[Dict[str, Any]]:
        """获取服务器配置

        Args:
            server_name: 服务器名称

        Returns:
            服务器配置
        """
        return self.config.get("mcpServers", {}).get(server_name)

    def get_all_servers(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务器配置

        Returns:
            所有服务器配置
        """
        return self.config.get("mcpServers", {})

    def update_server_config(self, server_name: str, config: Dict[str, Any]) -> None:
        """更新服务器配置

        Args:
            server_name: 服务器名称
            config: 服务器配置
        """
        if "mcpServers" not in self.config:
            self.config["mcpServers"] = {}
        self.config["mcpServers"][server_name] = config
        self._save_config(self.config)

    def remove_server_config(self, server_name: str) -> None:
        """删除服务器配置

        Args:
            server_name: 服务器名称
        """
        if "mcpServers" in self.config and server_name in self.config["mcpServers"]:
            del self.config["mcpServers"][server_name]
            self._save_config(self.config)

            # 如果服务正在运行，停止它
            self.stop_server(server_name)

    def toggle_server_status(self, server_name: str, enabled: bool) -> None:
        """切换服务器状态

        Args:
            server_name: 服务器名称
            enabled: 是否启用
        """
        if "mcpServers" in self.config and server_name in self.config["mcpServers"]:
            self.config["mcpServers"][server_name]["enabled"] = enabled
            self._save_config(self.config)

            # 根据状态启动或停止服务
            if enabled:
                self.start_server(server_name)
            else:
                self.stop_server(server_name)

    def start_server(self, server_name: str) -> bool:
        """启动MCP服务端

        Args:
            server_name: 服务器名称

        Returns:
            是否启动成功
        """
        server_config = self.get_server_config(server_name)
        if not server_config:
            logging.error(f"服务器配置不存在: {server_name}")
            return False

        if not server_config.get("enabled", False):
            logging.error(f"服务器未启用: {server_name}")
            return False

        # 检查配置类型
        connection_type = server_config.get("connection_type", "stdio")

        # 对于HTTP类型的连接，不需要启动本地进程
        if connection_type == "http":
            logging.info(f"HTTP类型MCP服务无需启动本地进程: {server_name}")
            # 初始化日志记录HTTP服务状态
            self.server_logs[server_name] = {
                "stdout": [f"[HTTP服务] {server_name} 配置为HTTP连接类型"],
                "stderr": [],
                "command": f"HTTP服务: {server_config.get('server_name', server_name)}",
                "start_time": time.time()
            }
            return True

        # 检查是否有command字段
        if "command" not in server_config:
            error_msg = f"服务器配置缺少command字段: {server_name}"
            logging.error(error_msg)
            self.server_logs[server_name] = {
                "stdout": [],
                "stderr": [f"[错误] {error_msg}"],
                "command": "",
                "start_time": time.time()
            }
            return False

        # 如果服务已经在运行，先停止它
        if server_name in self.server_processes:
            self.stop_server(server_name)

        try:
            # 构建命令
            command = [server_config["command"]] + server_config.get("args", [])

            # 设置环境变量
            env = os.environ.copy()
            if server_config.get("env"):
                env.update(server_config["env"])

            # 初始化日志
            self.server_logs[server_name] = {
                "stdout": [],
                "stderr": [],
                "command": " ".join(command),
                "start_time": time.time()
            }

            # 启动进程
            process = subprocess.Popen(
                command,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1
            )

            # 存储进程
            self.server_processes[server_name] = process

            # 保存PID到文件
            self._save_pid(server_name, process.pid)

            # 记录启动日志
            self.server_logs[server_name]["stdout"].append(f"[启动] 服务已启动，PID: {process.pid}")
            self.server_logs[server_name]["stdout"].append(f"[启动] 命令: {' '.join(command)}")
            self.server_logs[server_name]["stdout"].append(f"[启动] 工作目录: {os.getcwd()}")

            # 等待一下检查进程是否立即退出
            time.sleep(0.5)

            if process.poll() is None:
                logging.info(f"已启动MCP服务端: {server_name} (PID: {process.pid})")
                self.server_logs[server_name]["stdout"].append(f"[启动] 进程启动成功，状态正常")
                return True
            else:
                # 进程立即退出了
                return_code = process.returncode
                stdout, stderr = process.communicate()

                error_msg = f"MCP服务启动后立即退出: {server_name} (返回码: {return_code})"
                logging.error(error_msg)

                self.server_logs[server_name]["stderr"].append(f"[错误] {error_msg}")
                if stdout:
                    self.server_logs[server_name]["stdout"].append(f"[输出] {stdout}")
                if stderr:
                    self.server_logs[server_name]["stderr"].append(f"[错误] {stderr}")

                # 清理
                del self.server_processes[server_name]
                self._remove_pid_file(server_name)
                return False
        except Exception as e:
            error_msg = f"启动MCP服务端失败: {server_name}, 错误: {e}"
            logging.error(error_msg)

            # 记录错误日志
            if server_name not in self.server_logs:
                self.server_logs[server_name] = {"stdout": [], "stderr": [], "command": "", "start_time": time.time()}
            self.server_logs[server_name]["stderr"].append(f"[错误] {error_msg}")

            return False

    def stop_server(self, server_name: str) -> bool:
        """停止MCP服务端

        Args:
            server_name: 服务器名称

        Returns:
            是否停止成功
        """
        if server_name in self.server_processes:
            try:
                process = self.server_processes[server_name]

                # 记录停止日志
                if server_name in self.server_logs:
                    self.server_logs[server_name]["stdout"].append(f"[停止] 正在停止服务...")

                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    if server_name in self.server_logs:
                        self.server_logs[server_name]["stdout"].append(f"[停止] 强制终止服务")

                del self.server_processes[server_name]

                # 删除PID文件
                self._remove_pid_file(server_name)

                # 记录停止完成日志
                if server_name in self.server_logs:
                    self.server_logs[server_name]["stdout"].append(f"[停止] 服务已停止")

                logging.info(f"已停止MCP服务端: {server_name}")
                return True
            except Exception as e:
                error_msg = f"停止MCP服务端失败: {server_name}, 错误: {e}"
                logging.error(error_msg)

                # 记录错误日志
                if server_name in self.server_logs:
                    self.server_logs[server_name]["stderr"].append(f"[错误] {error_msg}")

                return False
        return True

    def start_all_enabled_servers(self) -> Dict[str, bool]:
        """启动所有启用的MCP服务端

        Returns:
            服务启动结果，键为服务名称，值为是否启动成功
        """
        results = {}
        for server_name, server_config in self.get_all_servers().items():
            if server_config.get("enabled", False):
                results[server_name] = self.start_server(server_name)
        return results

    def stop_all_servers(self) -> Dict[str, bool]:
        """停止所有MCP服务端

        Returns:
            服务停止结果，键为服务名称，值为是否停止成功
        """
        results = {}
        for server_name in list(self.server_processes.keys()):
            results[server_name] = self.stop_server(server_name)
        return results

    def get_server_status(self, server_name: str) -> str:
        """获取服务器状态

        Args:
            server_name: 服务器名称

        Returns:
            服务器状态，"running"表示运行中，"stopped"表示已停止，"disabled"表示已禁用，"not_found"表示未找到
        """
        server_config = self.get_server_config(server_name)
        if not server_config:
            return "not_found"

        if not server_config.get("enabled", False):
            return "disabled"

        # 对于HTTP类型的连接，检查是否有日志记录
        connection_type = server_config.get("connection_type", "stdio")
        if connection_type == "http":
            # HTTP服务不需要本地进程，只要enabled就认为是running
            return "running"

        if server_name in self.server_processes:
            process = self.server_processes[server_name]
            if process.poll() is None:
                return "running"
            else:
                # 进程已结束，清理
                del self.server_processes[server_name]
                self._remove_pid_file(server_name)
                return "stopped"

        # 如果进程不在内存中，检查PID文件
        pid = self._load_pid(server_name)
        if pid and self._is_process_running(pid):
            logging.info(f"通过PID文件发现运行中的MCP服务: {server_name} (PID: {pid})")
            return "running"
        elif pid:
            # PID文件存在但进程不存在，清理PID文件
            logging.info(f"清理无效的PID文件: {server_name} (PID: {pid})")
            self._remove_pid_file(server_name)

        # 最后尝试通过命令检查进程
        if self._check_process_by_command(server_config):
            logging.info(f"通过命令检查发现运行中的MCP服务: {server_name}")
            return "running"

        return "stopped"

    def get_all_server_status(self) -> Dict[str, str]:
        """获取所有服务器状态

        Returns:
            所有服务器状态，键为服务名称，值为服务器状态
        """
        return {server_name: self.get_server_status(server_name) for server_name in self.get_all_servers()}

    def get_server_logs(self, server_name: str) -> Dict[str, Any]:
        """获取指定服务的日志

        Args:
            server_name: 服务名称

        Returns:
            服务日志信息
        """
        if server_name in self.server_logs:
            logs = self.server_logs[server_name].copy()

            # 获取服务配置以检查连接类型
            server_config = self.get_server_config(server_name)
            connection_type = server_config.get("connection_type", "stdio") if server_config else "stdio"

            # 对于HTTP类型的连接，检查enabled状态
            if connection_type == "http":
                if server_config and server_config.get("enabled", False):
                    logs["status"] = "运行中"
                else:
                    logs["status"] = "未运行"
            else:
                # 对于stdio类型，检查进程状态
                if server_name in self.server_processes:
                    process = self.server_processes[server_name]

                    # 检查进程状态
                    if process.poll() is None:
                        logs["status"] = "运行中"
                    else:
                        # 进程已结束
                        return_code = getattr(process, 'returncode', 0)
                        logs["status"] = f"已结束 (返回码: {return_code})"
                        if return_code != 0:
                            logs["stderr"].append(f"[错误] 进程异常退出，返回码: {return_code}")
                else:
                    # 如果进程不在内存中，检查PID文件和系统进程
                    logs["stdout"].append("[检查] 进程不在内存中，正在检查PID文件...")

                    pid = self._load_pid(server_name)
                    if pid and self._is_process_running(pid):
                        logs["status"] = "运行中"
                        logs["stdout"].append(f"[恢复] 通过PID文件发现运行中的服务，PID: {pid}")
                    elif pid:
                        # PID文件存在但进程不存在
                        logs["stdout"].append(f"[清理] PID文件存在但进程已结束，PID: {pid}")
                        self._remove_pid_file(server_name)
                        logs["status"] = "未运行"
                    else:
                        # 没有PID文件，尝试通过命令检查
                        logs["stdout"].append("[检查] 无PID文件，尝试通过命令检查进程...")
                        if self._check_process_by_command(server_config):
                            logs["status"] = "运行中"
                            logs["stdout"].append("[恢复] 通过命令检查发现运行中的服务进程")
                        else:
                            logs["status"] = "未运行"
                            logs["stdout"].append("[检查] 未发现运行中的进程")

            return logs
        else:
            return {"stdout": [], "stderr": [], "command": "", "start_time": 0, "status": "无日志"}

    def get_all_server_logs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务的日志

        Returns:
            所有服务的日志信息
        """
        all_logs = {}
        for server_name in self.server_logs:
            all_logs[server_name] = self.get_server_logs(server_name)
        return all_logs

    def clear_server_logs(self, server_name: str) -> bool:
        """清除指定服务的日志

        Args:
            server_name: 服务名称

        Returns:
            是否清除成功
        """
        if server_name in self.server_logs:
            self.server_logs[server_name]["stdout"] = []
            self.server_logs[server_name]["stderr"] = []
            return True
        return False


# 创建MCP服务端管理器实例
mcp_server_manager = MCPServerManager()
