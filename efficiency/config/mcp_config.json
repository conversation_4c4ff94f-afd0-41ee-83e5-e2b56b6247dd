{"mcpServers": {"files": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "."], "enabled": true}, "@wopal-mcp-server-hotnews": {"command": "npx", "args": ["@wopal/mcp-server-hotnews"], "enabled": false}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": false}, "duckduckgo-mcp-server": {"command": "npx", "args": ["-y", "@nickclyde/duckduckgo-mcp-server"], "connection_type": "stdio", "enabled": false}, "duckduckgo-remote": {"connection_type": "http", "server_name": "@nickclyde/duckduckgo-mcp-server", "api_key": "8b943f07-94c8-487e-954b-d6068a08e306", "profile_id": "", "config": {}, "enabled": true}, "browserbase": {"command": "npx", "args": ["-y", "@browserbasehq/mcp-browserbase"], "connection_type": "stdio", "env": {"BROWSERBASE_API_KEY": "", "BROWSERBASE_PROJECT_ID": ""}, "enabled": false}}}